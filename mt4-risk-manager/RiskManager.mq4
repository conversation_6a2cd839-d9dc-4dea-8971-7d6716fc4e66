//+------------------------------------------------------------------+
//|                                         RiskManager_Simple.mq4 |
//|                                  风险管理插件 - 简化兼容版本      |
//+------------------------------------------------------------------+
#property copyright "Risk Management Tool - Simple Version"
#property version   "1.00"
#property strict

// 输入参数
input double InitialRiskAmount = 100.0; // 初始最大亏损金额
input color StopLossColor = clrRed;     // 止损线颜色
input color TakeProfitColor = clrGreen; // 止盈线颜色
input int PanelX = 10;                  // 面板X坐标
input int PanelY = 30;                  // 面板Y坐标

// 全局变量
double MaxRiskAmount = 100.0;
double currentPrice = 0;
double stopLossPrice = 0;
double takeProfitPrice = 0;
double calculatedLots = 0;
double riskRatio = 0;
double stopLossAmount = 0;
double takeProfitAmount = 0;
bool isLongPosition = true;

// 拖拽状态跟踪
bool isDraggingSL = false;
bool isDraggingTP = false;
double lastCheckSL = 0;  // 添加这两个变量来跟踪上次检查的价格
double lastCheckTP = 0;

// 对象名称
string slLineName = "RM_SL_Line";  // 修改名称避免冲突
string tpLineName = "RM_TP_Line";  // 修改名称避免冲突
string panelPrefix = "RM_";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    MaxRiskAmount = InitialRiskAmount;

    // 获取当前价格并设置初始线条位置
    currentPrice = Bid;
    stopLossPrice = currentPrice - 50 * Point;
    takeProfitPrice = currentPrice + 100 * Point;
    
    // 初始化上次检查的价格
    lastCheckSL = stopLossPrice;
    lastCheckTP = takeProfitPrice;

    // 创建止损止盈线
    CreateLines();

    // 创建简单面板
    CreateSimplePanel();

    // 初始计算
    CalculatePosition();
    UpdateDisplay();

    // 启动定时器 - 更高频率检查
    EventSetTimer(1);

    // 启用图表事件
    ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, true);
    ChartSetInteger(0, CHART_EVENT_OBJECT_CREATE, true);
    ChartSetInteger(0, CHART_EVENT_OBJECT_DELETE, true);

    Print("风险管理工具初始化完成");
    Print("请直接用鼠标拖拽红色止损线和绿色止盈线");
    
    // 强制窗口重绘
    ChartRedraw();
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 停止定时器
    EventKillTimer();

    // 关闭鼠标移动事件
    ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, false);

    // 删除所有对象
    ObjectDelete(slLineName);
    ObjectDelete(tpLineName);

    // 删除面板对象
    for(int i = ObjectsTotal() - 1; i >= 0; i--)
    {
        string objName = ObjectName(i);
        if(StringFind(objName, panelPrefix) == 0)
        {
            ObjectDelete(objName);
        }
    }
    
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    currentPrice = Bid;
    
    // 每个tick都检查线条位置
    CheckLinePositions();
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
{
    // 定时检查线条位置
    CheckLinePositions();
}

//+------------------------------------------------------------------+
//| Chart event function                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    // 调试信息
    if(id != CHARTEVENT_MOUSE_MOVE)  // 避免鼠标移动事件刷屏
    {
        Print("图表事件: ID=", id, ", sparam=", sparam);
    }
    
    // 鼠标移动事件
    if(id == CHARTEVENT_MOUSE_MOVE)
    {
        // 实时检查位置
        CheckLinePositions();
    }

    // 对象拖拽事件
    if(id == CHARTEVENT_OBJECT_DRAG)
    {
        Print("*** 检测到拖拽事件! 对象: ", sparam, " ***");
        
        if(sparam == slLineName || sparam == tpLineName)
        {
            CheckLinePositions();
        }
    }

    // 对象点击事件
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == panelPrefix + "BuyBtn")
        {
            isLongPosition = true;
            ExecuteTrade();
        }
        else if(sparam == panelPrefix + "SellBtn")
        {
            isLongPosition = false;
            ExecuteTrade();
        }
        else if(sparam == slLineName || sparam == tpLineName)
        {
            Print("线条被点击: ", sparam);
            CheckLinePositions();
        }
    }

    // 对象属性改变事件
    if(id == CHARTEVENT_OBJECT_CHANGE)
    {
        if(sparam == slLineName || sparam == tpLineName)
        {
            Print("对象属性改变: ", sparam);
            CheckLinePositions();
        }
    }
}

//+------------------------------------------------------------------+
//| 创建线条                                                          |
//+------------------------------------------------------------------+
void CreateLines()
{
    // 确保删除已存在的线条
    ObjectDelete(slLineName);
    ObjectDelete(tpLineName);

    // 创建止损线
    if(ObjectCreate(slLineName, OBJ_HLINE, 0, 0, stopLossPrice))
    {
        ObjectSet(slLineName, OBJPROP_COLOR, StopLossColor);
        ObjectSet(slLineName, OBJPROP_WIDTH, 3);  // 加粗便于选中
        ObjectSet(slLineName, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSet(slLineName, OBJPROP_SELECTABLE, true);
        ObjectSet(slLineName, OBJPROP_SELECTED, false);
        ObjectSet(slLineName, OBJPROP_BACK, false);
        
        // 设置描述文本
        ObjectSetString(0, slLineName, OBJPROP_TEXT, 
                       "止损线 - 价格: " + DoubleToString(stopLossPrice, Digits));
        
        Print("止损线创建成功");
    }
    else
    {
        Print("止损线创建失败，错误: ", GetLastError());
    }

    // 创建止盈线
    if(ObjectCreate(tpLineName, OBJ_HLINE, 0, 0, takeProfitPrice))
    {
        ObjectSet(tpLineName, OBJPROP_COLOR, TakeProfitColor);
        ObjectSet(tpLineName, OBJPROP_WIDTH, 3);  // 加粗便于选中
        ObjectSet(tpLineName, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSet(tpLineName, OBJPROP_SELECTABLE, true);
        ObjectSet(tpLineName, OBJPROP_SELECTED, false);
        ObjectSet(tpLineName, OBJPROP_BACK, false);
        
        // 设置描述文本
        ObjectSetString(0, tpLineName, OBJPROP_TEXT, 
                       "止盈线 - 价格: " + DoubleToString(takeProfitPrice, Digits));
        
        Print("止盈线创建成功");
    }
    else
    {
        Print("止盈线创建失败，错误: ", GetLastError());
    }

    ChartRedraw();
}

//+------------------------------------------------------------------+
//| 创建简单面板                                                      |
//+------------------------------------------------------------------+
void CreateSimplePanel()
{
    // 背景
    ObjectCreate(panelPrefix + "Background", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSet(panelPrefix + "Background", OBJPROP_XDISTANCE, PanelX);
    ObjectSet(panelPrefix + "Background", OBJPROP_YDISTANCE, PanelY);
    ObjectSet(panelPrefix + "Background", OBJPROP_XSIZE, 200);
    ObjectSet(panelPrefix + "Background", OBJPROP_YSIZE, 150);
    ObjectSet(panelPrefix + "Background", OBJPROP_BGCOLOR, clrLightGray);
    ObjectSet(panelPrefix + "Background", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    
    // 标题
    CreateLabel(panelPrefix + "Title", PanelX + 10, PanelY + 10, 
                "风险管理", 10, "Arial Bold", clrBlack);
    
    // 显示标签
    CreateLabel(panelPrefix + "LotsLabel", PanelX + 10, PanelY + 35,
                "手数: 0.00", 9, "Arial", clrBlue);
    
    CreateLabel(panelPrefix + "RatioLabel", PanelX + 10, PanelY + 55,
                "盈亏比: 0:1", 9, "Arial", clrBlue);
    
    CreateLabel(panelPrefix + "RiskLabel", PanelX + 10, PanelY + 75,
                "风险: $" + DoubleToString(MaxRiskAmount, 2), 9, "Arial", clrRed);
    
    CreateLabel(panelPrefix + "ProfitLabel", PanelX + 10, PanelY + 95,
                "预期盈利: $0.00", 9, "Arial", clrGreen);
    
    // 交易按钮
    CreateButton(panelPrefix + "BuyBtn", PanelX + 10, PanelY + 120,
                 70, 25, "买入", clrLightGreen);
    
    CreateButton(panelPrefix + "SellBtn", PanelX + 90, PanelY + 120,
                 70, 25, "卖出", clrLightPink);
}

//+------------------------------------------------------------------+
//| 创建标签辅助函数                                                   |
//+------------------------------------------------------------------+
void CreateLabel(string name, int x, int y, string text, int size, string font, color clr)
{
    ObjectCreate(name, OBJ_LABEL, 0, 0, 0);
    ObjectSet(name, OBJPROP_XDISTANCE, x);
    ObjectSet(name, OBJPROP_YDISTANCE, y);
    ObjectSetText(name, text, size, font, clr);
}

//+------------------------------------------------------------------+
//| 创建按钮辅助函数                                                   |
//+------------------------------------------------------------------+
void CreateButton(string name, int x, int y, int width, int height, string text, color bgColor)
{
    ObjectCreate(name, OBJ_BUTTON, 0, 0, 0);
    ObjectSet(name, OBJPROP_XDISTANCE, x);
    ObjectSet(name, OBJPROP_YDISTANCE, y);
    ObjectSet(name, OBJPROP_XSIZE, width);
    ObjectSet(name, OBJPROP_YSIZE, height);
    ObjectSetText(name, text);
    ObjectSet(name, OBJPROP_BGCOLOR, bgColor);
}

//+------------------------------------------------------------------+
//| 检查线条位置 - 核心函数                                            |
//+------------------------------------------------------------------+
void CheckLinePositions()
{
    bool needUpdate = false;
    
    // 检查止损线
    if(ObjectFind(slLineName) >= 0)
    {
        double currentSL = ObjectGet(slLineName, OBJPROP_PRICE1);
        
        // 使用更小的阈值来检测变化
        if(MathAbs(currentSL - lastCheckSL) >= Point/2)
        {
            Print("止损线移动检测: 从 ", DoubleToString(lastCheckSL, Digits), 
                  " 到 ", DoubleToString(currentSL, Digits));
            
            stopLossPrice = currentSL;
            lastCheckSL = currentSL;
            needUpdate = true;
            
            // 更新线条描述
            ObjectSetString(0, slLineName, OBJPROP_TEXT, 
                           "止损线 - 价格: " + DoubleToString(currentSL, Digits));
        }
        
        // 检查选中状态
        bool isSelected = ObjectGetInteger(0, slLineName, OBJPROP_SELECTED);
        if(isSelected != isDraggingSL)
        {
            isDraggingSL = isSelected;
            Print("止损线选中状态: ", isSelected ? "已选中" : "未选中");
        }
    }
    
    // 检查止盈线
    if(ObjectFind(tpLineName) >= 0)
    {
        double currentTP = ObjectGet(tpLineName, OBJPROP_PRICE1);
        
        // 使用更小的阈值来检测变化
        if(MathAbs(currentTP - lastCheckTP) >= Point/2)
        {
            Print("止盈线移动检测: 从 ", DoubleToString(lastCheckTP, Digits), 
                  " 到 ", DoubleToString(currentTP, Digits));
            
            takeProfitPrice = currentTP;
            lastCheckTP = currentTP;
            needUpdate = true;
            
            // 更新线条描述
            ObjectSetString(0, tpLineName, OBJPROP_TEXT, 
                           "止盈线 - 价格: " + DoubleToString(currentTP, Digits));
        }
        
        // 检查选中状态
        bool isSelected = ObjectGetInteger(0, tpLineName, OBJPROP_SELECTED);
        if(isSelected != isDraggingTP)
        {
            isDraggingTP = isSelected;
            Print("止盈线选中状态: ", isSelected ? "已选中" : "未选中");
        }
    }
    
    // 如果有变化，更新计算和显示
    if(needUpdate)
    {
        CalculatePosition();
        UpdateDisplay();
    }
}

//+------------------------------------------------------------------+
//| 计算仓位                                                          |
//+------------------------------------------------------------------+
void CalculatePosition()
{
    if(stopLossPrice <= 0 || takeProfitPrice <= 0 || currentPrice <= 0)
        return;

    double stopLossPoints = MathAbs(currentPrice - stopLossPrice) / Point;
    double takeProfitPoints = MathAbs(takeProfitPrice - currentPrice) / Point;
    
    double tickValue = MarketInfo(Symbol(), MODE_TICKVALUE);
    if(tickValue <= 0) tickValue = 1.0;

    if(stopLossPoints > 0)
    {
        calculatedLots = MaxRiskAmount / (stopLossPoints * tickValue);
        
        double minLot = MarketInfo(Symbol(), MODE_MINLOT);
        double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
        double lotStep = MarketInfo(Symbol(), MODE_LOTSTEP);
        
        calculatedLots = MathMax(minLot, MathMin(maxLot,
                        MathFloor(calculatedLots / lotStep) * lotStep));
        
        riskRatio = takeProfitPoints / stopLossPoints;
        stopLossAmount = calculatedLots * stopLossPoints * tickValue;
        takeProfitAmount = calculatedLots * takeProfitPoints * tickValue;
    }
    else
    {
        calculatedLots = 0;
        riskRatio = 0;
        stopLossAmount = 0;
        takeProfitAmount = 0;
    }
}

//+------------------------------------------------------------------+
//| 更新显示                                                          |
//+------------------------------------------------------------------+
void UpdateDisplay()
{
    ObjectSetText(panelPrefix + "LotsLabel", 
                  "手数: " + DoubleToString(calculatedLots, 2));
    ObjectSetText(panelPrefix + "RatioLabel", 
                  "盈亏比: " + DoubleToString(riskRatio, 2) + ":1");
    ObjectSetText(panelPrefix + "RiskLabel", 
                  "风险: $" + DoubleToString(stopLossAmount, 2));
    ObjectSetText(panelPrefix + "ProfitLabel", 
                  "预期盈利: $" + DoubleToString(takeProfitAmount, 2));

    ChartRedraw();
}

//+------------------------------------------------------------------+
//| 执行交易                                                          |
//+------------------------------------------------------------------+
void ExecuteTrade()
{
    if(calculatedLots <= 0)
    {
        Alert("手数计算错误！请检查止损止盈位置。");
        return;
    }

    // 检查止损止盈位置是否合理
    if(isLongPosition)
    {
        if(stopLossPrice >= Bid || takeProfitPrice <= Ask)
        {
            Alert("买入订单的止损止盈位置不正确！");
            return;
        }
    }
    else
    {
        if(stopLossPrice <= Ask || takeProfitPrice >= Bid)
        {
            Alert("卖出订单的止损止盈位置不正确！");
            return;
        }
    }

    int orderType = isLongPosition ? OP_BUY : OP_SELL;
    double openPrice = isLongPosition ? Ask : Bid;

    int ticket = OrderSend(Symbol(), orderType, calculatedLots, openPrice, 3,
                          stopLossPrice, takeProfitPrice,
                          "风险管理工具", 0, 0,
                          isLongPosition ? clrGreen : clrRed);

    if(ticket > 0)
    {
        Alert("订单成功！\n" +
              "订单号: " + IntegerToString(ticket) + "\n" +
              "手数: " + DoubleToString(calculatedLots, 2) + "\n" +
              "盈亏比: " + DoubleToString(riskRatio, 2) + ":1\n" +
              "预期风险: $" + DoubleToString(stopLossAmount, 2) + "\n" +
              "预期盈利: $" + DoubleToString(takeProfitAmount, 2));
    }
    else
    {
        Alert("订单失败！错误: " + IntegerToString(GetLastError()));
    }
}
